<template>
  <el-dialog
    v-model="dialogVisible"
    title="专家详情"
    width="80%"
    :before-close="handleClose"
    class="expert-detail-dialog"
    top="5vh"
  >
    <div v-if="expert" class="expert-detail-content">
      <!-- 专家基本信息 -->
      <div class="expert-header">
        <div class="expert-avatar-section">
          <div class="expert-avatar-large">
            <img :src="expert.avatar || defaultAvatar" :alt="expert.name" />
          </div>
        </div>
        <div class="expert-basic-info">
          <div class="expert-name-title">
            <h2 class="expert-name">姓名：{{ expert.name }}</h2>
            <span class="expert-title">{{ expert.work }}</span>
          </div>
          <div class="expert-tags-section">
            <label>专业标签：</label>
            <span v-for="tag in expert.tags" :key="tag" class="expert-tag">
              {{ tag }}
            </span>
          </div>
          <div class="expert-contact-info">
            <div class="contact-item">
              <label>职称：</label>
              <span>{{ expert.work }}</span>
            </div>
            <div class="contact-item">
              <label>电话：</label>
              <span>{{ expert.phone }}</span>
            </div>
            <div class="contact-item">
              <label>工作单位：</label>
              <span>{{ expert.department }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细介绍 -->
      <div class="expert-section">
        <h3 class="section-title">详细介绍</h3>
        <div class="section-content">
          <p class="expert-introduction">{{ expert.introduction }}</p>
        </div>
      </div>

      <!-- 专业信息 -->
      <div class="expert-section">
        <h3 class="section-title">专业信息</h3>
        <div class="section-content">
          <div class="info-grid">
            <div class="info-item">
              <label>学历：</label>
              <span>{{ expert.education }}</span>
            </div>
            <div class="info-item">
              <label>研究方向：</label>
              <span>{{ expert.researchDirection }}</span>
            </div>
            <div class="info-item">
              <label>从业经验：</label>
              <span>{{ expert.experience }}</span>
            </div>
            <div class="info-item">
              <label>邮箱：</label>
              <span>{{ expert.email }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 专业特长 -->
      <div class="expert-section" v-if="expert.specialties && expert.specialties.length">
        <h3 class="section-title">专业特长</h3>
        <div class="section-content">
          <div class="specialties-list">
            <span v-for="specialty in expert.specialties" :key="specialty" class="specialty-tag">
              {{ specialty }}
            </span>
          </div>
        </div>
      </div>

      <!-- 主要成就 -->
      <div class="expert-section" v-if="expert.achievements && expert.achievements.length">
        <h3 class="section-title">主要成就</h3>
        <div class="section-content">
          <ul class="achievements-list">
            <li v-for="achievement in expert.achievements" :key="achievement">
              {{ achievement }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 主要项目 -->
      <div class="expert-section" v-if="expert.projects && expert.projects.length">
        <h3 class="section-title">主要项目</h3>
        <div class="section-content">
          <div class="projects-list">
            <div v-for="project in expert.projects" :key="project.name" class="project-item">
              <div class="project-header">
                <h4 class="project-name">{{ project.name }}</h4>
                <span class="project-year">{{ project.year }}</span>
              </div>
              <div class="project-details">
                <span class="project-funding">资助来源：{{ project.funding }}</span>
                <span class="project-role">担任角色：{{ project.role }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要论文 -->
      <div class="expert-section" v-if="expert.publications && expert.publications.length">
        <h3 class="section-title">主要论文</h3>
        <div class="section-content">
          <div class="publications-list">
            <div
              v-for="publication in expert.publications"
              :key="publication.title"
              class="publication-item"
            >
              <div class="publication-title">{{ publication.title }}</div>
              <div class="publication-details">
                <span class="publication-journal">{{ publication.journal }}</span>
                <span class="publication-year">{{ publication.year }}</span>
                <span class="publication-impact">影响因子：{{ publication.impact }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleContact">
          <i class="el-icon-chat-dot-round"></i>
          立即咨询
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'ExpertDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    expert: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      defaultAvatar: '/src/assets/imgs/avatar.jpg'
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        if (!value) {
          this.handleClose()
        }
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleContact() {
      if (this.expert) {
        this.$message.success(`正在联系 ${this.expert.name}`)
        // 这里可以添加实际的联系逻辑
      }
    }
  }
}
</script>
